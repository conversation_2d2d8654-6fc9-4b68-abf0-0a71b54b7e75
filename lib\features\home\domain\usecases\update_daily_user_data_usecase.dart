import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/features/home/<USER>/repositories/home_repository.dart';
import 'package:injectable/injectable.dart';

@injectable
class UpdateDailyUserDataUseCase {
  final HomeRepository repository;

  UpdateDailyUserDataUseCase({required this.repository});

  Future<void> call(DailyUserDataModel model) {
    return repository.updateDailyUserData(model);
  }
}
