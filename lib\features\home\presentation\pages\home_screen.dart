import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart';
import 'package:cal/features/home/<USER>/bloc/recent_food_bloc.dart';
import 'package:cal/features/home/<USER>/widgets/food_card/food_card.dart';
import 'package:cal/features/home/<USER>/widgets/home_appbar.dart';
import 'package:cal/features/home/<USER>/widgets/nutritions_summery.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../common/widgets/metric_card.dart';
import '../widgets/date_list.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ValueNotifier<DateTime> _dateValueNotifier = ValueNotifier<DateTime>(DateTime.now());

  @override
  void initState() {
    super.initState();

    _dateValueNotifier.addListener(_onDateChanged);
    // _onDateChanged();
  }

  @override
  void dispose() {
    _dateValueNotifier.removeListener(_onDateChanged);
    _dateValueNotifier.dispose();
    super.dispose();
  }

  void _onDateChanged() {
    context.read<RecentFoodBloc>().add(LoadFood(date: _dateValueNotifier.value));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<NutritionBloc>(
      lazy: false,
      create: (context) => getIt<NutritionBloc>()..add(InitDailyUserData(date: _dateValueNotifier.value)),
      child: BlocListener<RecentFoodBloc, RecentFoodState>(
        listener: (context, state) {
          Future.delayed(const Duration(seconds: 1), () {
            if (context.mounted) {
              context.read<NutritionBloc>().add(LoadDailyNutritionData(date: _dateValueNotifier.value));
            }
          });
        },
        child: Scaffold(
          appBar: homeAppBar(context),
          body: SingleChildScrollView(
            padding: const EdgeInsetsDirectional.only(end: 16, start: 16, top: 0, bottom: 50),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<NutritionBloc, NutritionState>(
                  builder: (context, state) {
                    return DateList(
                      selectedDateNotifier: _dateValueNotifier,
                      todayCals: calculateSafeProgress(state.targetCalories, state.consumedCalories),
                    );
                  },
                ),
                const SizedBox(height: 20),
                const NutritionSummary(),
                const SizedBox(height: 30),
                AppText.titleLarge(LocaleKeys.home_recently_added.tr(), color: context.onSecondary, fontWeight: FontWeight.bold),
                const SizedBox(height: 20),
                BlocBuilder<RecentFoodBloc, RecentFoodState>(
                  builder: (context, state) {
                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.foodList.length,
                      itemBuilder: (context, index) {
                        return FoodCard(
                          foodModel: state.foodList[index],
                          onDelete: () => context.read<RecentFoodBloc>().add(DeleteFood(state.foodList[index])),
                        );
                      },
                      separatorBuilder: (context, index) => const SizedBox(height: 16),
                    );
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
