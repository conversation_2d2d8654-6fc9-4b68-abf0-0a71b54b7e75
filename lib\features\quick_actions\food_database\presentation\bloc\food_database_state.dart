part of 'food_database_bloc.dart';

enum FoodDatabaseStatus { initial, loading, success, failure }

class FoodDatabaseState extends Equatable {
  final FoodDatabaseStatus status;
  final List<DatabaseFoodModel> foodList;
  final List<DatabaseFoodModel> recentFoodList;
  final List<DatabaseFoodModel> myMealsList;
  final List<DatabaseFoodModel> myFavoriteList;
  final List<DatabaseFoodModel> databaseFoodList;
  final List<DatabaseFoodModel> selectedFoods;
  final String? errorMessage;

  final RemoteFoodDatabaseModel? searchedFood;
  final BlocStatus? searchedFoodStatus;

  const FoodDatabaseState({
    this.status = FoodDatabaseStatus.initial,
    this.foodList = const [],
    this.recentFoodList = const [],
    this.databaseFoodList = const [],
    this.myMealsList = const [],
    this.myFavoriteList = const [],
    this.selectedFoods = const [],
    this.errorMessage,
    this.searchedFood,
    this.searchedFoodStatus,
  });

  FoodDatabaseState copyWith({
    FoodDatabaseStatus? status,
    List<DatabaseFoodModel>? foodList,
    List<DatabaseFoodModel>? recentFoodList,
    List<DatabaseFoodModel>? myMealsList,
    List<DatabaseFoodModel>? myFavoriteList,
    List<DatabaseFoodModel>? databaseFoodList,
    List<DatabaseFoodModel>? selectedFoods,
    String? errorMessage,
    RemoteFoodDatabaseModel? searchedFood,
    BlocStatus? searchedFoodStatus,
  }) {
    return FoodDatabaseState(
      status: status ?? this.status,
      foodList: foodList ?? this.foodList,
      recentFoodList: recentFoodList ?? this.recentFoodList,
      selectedFoods: selectedFoods ?? this.selectedFoods,
      myMealsList: myMealsList ?? this.myMealsList,
      myFavoriteList: myFavoriteList ?? this.myFavoriteList,
      databaseFoodList: databaseFoodList ?? this.databaseFoodList,
      errorMessage: errorMessage ?? this.errorMessage,
      searchedFood: searchedFood ?? this.searchedFood,
      searchedFoodStatus: searchedFoodStatus ?? this.searchedFoodStatus,
    );
  }

  @override
  List<Object?> get props =>
      [status, foodList, recentFoodList, databaseFoodList, errorMessage, myFavoriteList, myMealsList, selectedFoods];
}
