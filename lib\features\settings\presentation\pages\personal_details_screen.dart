import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';

import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/core/local_models/daily_data_model/daily_user_info_service.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:isar/isar.dart';

import '../../../../core/datasources/user_local_data_source.dart';
import '../../../../core/di/injection.dart';
import '../../../../core/local_models/user_model/user_model.dart';
import '../../../../generated/assets.dart';
import '../widgets/personal_details_app_bar.dart';
import 'change_birth_date_screen.dart';
import 'change_gender_screen.dart';
import 'change_height_weight_screen.dart';
import 'change_weight_target_screen.dart';

class PersonalDetailsScreen extends StatefulWidget {
  const PersonalDetailsScreen({super.key});

  @override
  State<PersonalDetailsScreen> createState() => _PersonalDetailsScreenState();
}

class _PersonalDetailsScreenState extends State<PersonalDetailsScreen> {
  final List<String> titles = [
    'الوزن الحالي',
    'الطول',
    'تاريخ الميلاد',
    'الجنس',
  ];

  List<String> values(UserModel user, DailyUserDataModel daily) => [
        '${daily.weight}',
        user.height!,
        DateFormat('yyyy/MM/dd').format(user.birthDate!),
        user.gender!,
      ];

  List<Widget> pages(UserModel user, DailyUserDataModel daily) => [
        ChangeHeightWeightScreen(
          height: double.parse(user.height!),
          weight: daily.weight,
          dailyUserDataModel: daily,
          user: user,
        ),
        ChangeHeightWeightScreen(
          height: double.parse(user.height!),
          weight: daily.weight,
          dailyUserDataModel: daily,
          user: user,
        ),
        ChangeBirthDateScreen(
          user: user,
        ),
        ChangeGenderScreen(
          user: user,
        ),
      ];

  @override
  void initState() {
    super.initState();
    fetchUserData();
  }

  UserModel? user;
  DailyUserDataModel? dailyData;

  fetchUserData() async {
    user = await UserLocalDataSource(getIt<Isar>()).getUserData();
    dailyData = await DailyUserInfoService.getDailyBmi(startDate: DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day));
    setState(() {});
    print(user);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 20),
          child: Column(
            children: [
              const PersonalDetailsAppBar(
                title: 'التفاصيل الشخصية',
              ),
              const SizedBox(height: 26),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(51),
                      blurRadius: 10,
                      offset: const Offset(-2, 4),
                    ),
                  ],
                ),
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الوزن المستهدف',
                          style: context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w400),
                        ),
                        const SizedBox(height: 4),
                        user == null
                            ? const SizedBox.shrink()
                            : Text(
                                user!.targetWeight!,
                                style: context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
                              )
                      ],
                    ),
                    InkWell(
                      onTap: () async {
                        await context.push(ChangeWeightTargetScreen(model: user!)).then((val) {
                          fetchUserData();
                        });
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: context.primaryColor,
                        ),
                        padding: const EdgeInsetsDirectional.symmetric(horizontal: 12, vertical: 6),
                        child: Text(
                          'تغيير الهدف',
                          style: context.textTheme.bodyMedium!.copyWith(
                            color: context.onPrimaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 18),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: context.onPrimaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: context.onSecondary.withAlpha(37),
                      blurRadius: 10,
                      offset: const Offset(-2, 4),
                    ),
                  ],
                ),
                child: user == null
                    ? const SizedBox.shrink()
                    : ListView.separated(
                        shrinkWrap: true,
                        padding: const EdgeInsetsDirectional.symmetric(horizontal: 18, vertical: 5),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) => personalDetailsCard(
                          index,
                          context,
                          user!,
                          onTap: () async {
                            await context.push(pages(user!, dailyData!)[index]).then((val) {
                              fetchUserData();
                            });
                          },
                        ),
                        separatorBuilder: (context, index) => Divider(
                          color: context.onSecondary.withAlpha(51),
                          thickness: 1,
                        ),
                        itemCount: titles.length,
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget personalDetailsCard(int index, BuildContext context, UserModel user, {required Function() onTap}) => Padding(
        padding: const EdgeInsetsDirectional.symmetric(vertical: 11),
        child: InkWell(
          onTap: onTap,
          child: Row(
            children: [
              Text(
                titles[index],
                style: context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              Text(
                values(user, dailyData!)[index],
                style: context.textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              const AppImage.asset(Assets.imagesEdit),
            ],
          ),
        ),
      );
}
