import 'package:bloc/bloc.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart';
import 'package:equatable/equatable.dart';

part 'exercise_event.dart';
part 'exercise_state.dart';

class ExerciseBloc extends Bloc<ExerciseEvent, ExerciseState> {
  final SaveExerciseUseCase saveExerciseUseCase;
  final SaveExerciseAiUseCase saveExerciseAiUseCase;

  ExerciseBloc({
    required this.saveExerciseUseCase,
    required this.saveExerciseAiUseCase,
  }) : super(ExerciseInitial()) {
    on<SaveExercise>(_onSaveExercise);
    on<SaveExerciseAi>(_onSaveExerciseAi);
  }

  Future<void> _onSaveExercise(
    SaveExercise event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(ExerciseLoading());
    final result = await saveExerciseUseCase(event.exercise);
    result.fold(
      (failure) => emit(ExerciseError(failure)),
      (_) => emit(ExerciseSaved()),
    );
  }

  Future<void> _onSaveExerciseAi(
    SaveExerciseAi event,
    Emitter<ExerciseState> emit,
  ) async {
    emit(ExerciseLoading());
    final result = await saveExerciseAiUseCase(event.exercise);
    result.fold(
      (failure) => emit(ExerciseError(failure)),
      (_) => emit(ExerciseSaved()),
    );
  }
}


