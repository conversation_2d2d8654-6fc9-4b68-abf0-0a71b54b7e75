import 'package:cal/features/quick_actions/exercise/data/models/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/domain/repositories/exercise_repository.dart';
import 'package:dartz/dartz.dart';

class SaveExerciseUseCase {
  final ExerciseRepository repository;

  SaveExerciseUseCase(this.repository);

  Future<Either<String, ExerciseModel>> call(ExerciseSaveModel exercise) {
    return repository.saveExercise(exercise);
  }
}
