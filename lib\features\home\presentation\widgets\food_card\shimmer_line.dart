import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cal/common/extentions/colors_extension.dart';

class ShimmerLine extends StatelessWidget {
  const ShimmerLine({
    super.key,
    this.width = double.infinity,
    this.borderRadius = 6.0,
  });

  final double width;
  final double borderRadius;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: context.tertiary,
      highlightColor: Colors.grey[200]!,
      period: const Duration(milliseconds: 1500),
      child: Container(
        width: width,
        height: 10,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          color: context.tertiary,
        ),
      ),
    );
  }
}
