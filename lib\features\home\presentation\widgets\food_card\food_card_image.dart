import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/common/extentions/colors_extension.dart';

class FoodCardImage extends StatelessWidget {
  const FoodCardImage({
    super.key,
    required this.foodModel,
    required this.progress,
    required this.isLoading,
  });

  final FoodModel foodModel;
  final double progress;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildImage(),
          if (_shouldShowProgressIndicator()) _buildProgressIndicator(context),
        ],
      ),
    );
  }

  Widget _buildImage() {
    return Stack(
      children: [
        _hasValidImagePath()
            ? Image.file(
                File(foodModel.imagePath!),
                width: 117,
                height: 107,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stack) => _buildFallbackImage(),
              )
            : _buildFallbackImage(),
        if (_shouldShowProgressIndicator())
          Container(
            width: 117,
            height: 107,
            color: Colors.black.withAlpha(40),
          ),
      ],
    );
  }

  Widget _buildFallbackImage() {
    return const AppImage.asset(
      Assets.imagesFoodFallback,
      width: 117,
      height: 107,
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    return Positioned(
      child: SizedBox(
        width: 85,
        height: 85,
        child: Stack(
          alignment: Alignment.center,
          children: [
            CircularProgressIndicator(
              value: progress,
              constraints: const BoxConstraints(minHeight: 75, minWidth: 75),
              strokeWidth: 8,
              backgroundColor: context.onPrimaryColor.withAlpha(25),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _hasValidImagePath() {
    return foodModel.imagePath != null && File(foodModel.imagePath!).existsSync();
  }

  bool _shouldShowProgressIndicator() {
    return isLoading;
  }
}
