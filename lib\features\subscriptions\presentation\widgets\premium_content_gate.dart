import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/features/subscriptions/presentation/helpers/payment_navigation_helper.dart';

/// A widget that wraps premium content and shows payment prompt for non-subscribers
class PremiumContentGate extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? description;
  final bool showPaymentButton;

  const PremiumContentGate({
    Key? key,
    required this.child,
    this.title,
    this.description,
    this.showPaymentButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SubscriptionBloc, SubscriptionState>(
      builder: (context, state) {
        if (state.hasActiveSubscription) {
          return child;
        }

        return _buildPremiumPrompt(context);
      },
    );
  }

  Widget _buildPremiumPrompt(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: context.primaryColor.withAlpha(22),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.workspace_premium_outlined,
              size: 50,
              color: context.primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            title ?? 'ميزة مميزة',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            description ?? 'هذه الميزة متاحة للمشتركين المميزين فقط',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: context.tertiary,
              height: 1.5,
            ),
          ),
          if (showPaymentButton) ...[
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () => PaymentNavigationHelper.navigateToPaymentPage(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'ترقية الاشتراك',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class PremiumBadgeOverlay extends StatelessWidget {
  final Widget child;
  final bool isPremium;
  final VoidCallback? onTap;

  const PremiumBadgeOverlay({
    Key? key,
    required this.child,
    required this.isPremium,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isPremium)
          Positioned(
            top: 8,
            right: 8,
            child: AppGestureDetector(
              onTap: onTap,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: context.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.workspace_premium,
                      size: 16,
                      color: Colors.white,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'مميز',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
