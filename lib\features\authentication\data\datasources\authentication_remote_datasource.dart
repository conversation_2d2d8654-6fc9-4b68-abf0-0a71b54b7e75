import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/auth_token_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cal/core/network/api_handler.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';

// Remote Data Source for Authentication
abstract class AuthenticationRemoteDataSource {
  Future<AuthTokenModel> signInWithApple();
  Future<AuthTokenModel> signInWithGoogle();
  Future<void> signOut();
}

class AuthenticationRemoteDataSourceImpl with ApiHandler implements AuthenticationRemoteDataSource {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final HTTPClient httpClient;

  AuthenticationRemoteDataSourceImpl({
    required FirebaseAuth firebaseAuth,
    required GoogleSignIn googleSignIn,
    required this.httpClient,
  })  : _firebaseAuth = firebaseAuth,
        _googleSignIn = googleSignIn;

  @override
  Future<AuthTokenModel> signInWithApple() async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      final user = userCredential.user;

      if (user == null) {
        throw Exception('User not found after Apple sign-in');
      }

      final idTokenResult = await user.getIdTokenResult();

      final String mobileId = await MobileIdHelper.getMobileId();
      final appleTokenData = {
        'authorization_code': appleCredential.authorizationCode,
        'identity_token': appleCredential.identityToken,
        'user_identifier': appleCredential.userIdentifier,
        'email': appleCredential.email,
        'given_name': appleCredential.givenName,
        'family_name': appleCredential.familyName,
        'mobile_id': mobileId,
      };

      final apiResult = await handleApiCall(
        apiCall: () => httpClient.post(
          '/api/auth/apple',
          data: appleTokenData,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      apiResult.fold(
        (failure) {
          throw Exception('API call failed during Apple Sign-In: $failure');
        },
        (response) {
          final token = response['token'];

          if (token != null && token is String) {
            ShPH.saveData(key: AppKeys.token, value: token);
          } else {
            throw Exception("Token not found in response.");
          }
        },
      );

      return AuthTokenModel(
        accessToken: idTokenResult.token!,
        expiresAt: idTokenResult.expirationTime!,
      );
    } catch (e) {
      throw Exception('Apple Sign-In failed: $e');
    }
  }

  @override
  Future<AuthTokenModel> signInWithGoogle() async {
    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google Sign-In was cancelled by user');
      }

      // Get Google authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      final user = userCredential.user;
      if (user == null) {
        throw Exception('User not found after Google sign-in');
      }

      final idTokenResult = await user.getIdTokenResult();

      final String mobileId = await MobileIdHelper.getMobileId();
      final googleTokenData = {
        'token': googleAuth.idToken,
        'mobile_id': mobileId,
      };

      final apiResult = await handleApiCall(
        apiCall: () => httpClient.post(
          '/api/auth/google',
          data: googleTokenData,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      apiResult.fold(
        (failure) {
          throw Exception('API call failed during Google Sign-In: $failure');
        },
        (response) {
          final token = response['token'];

          if (token != null && token is String) {
            ShPH.saveData(key: AppKeys.token, value: token);
          } else {
            throw Exception("Token not found in response.");
          }
        },
      );

      return AuthTokenModel(
        accessToken: idTokenResult.token!,
        expiresAt: idTokenResult.expirationTime!,
      );
    } catch (e) {
      throw Exception('Google Sign-In failed: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
    } catch (e) {
      throw Exception('Sign-out failed: $e');
    }
  }
}
