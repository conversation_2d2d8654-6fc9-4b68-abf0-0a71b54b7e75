part of 'food_database_bloc.dart';

class FoodDatabaseEvent extends Equatable {
  const FoodDatabaseEvent();

  @override
  List<Object> get props => [];
}

class AddFoodEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel meal;

  const AddFoodEvent({required this.meal});

  @override
  List<Object> get props => [meal];
}

class AddFoodToLogEvent extends FoodDatabaseEvent {
  final FoodModel meal;

  const AddFoodToLogEvent({required this.meal});

  @override
  List<Object> get props => [meal];
}

class AddSelectedFoodEvent extends FoodDatabaseEvent {
  final DatabaseFoodModel food;

  const AddSelectedFoodEvent(this.food);
}

class LoadRecentFoodEvent extends FoodDatabaseEvent {
  const LoadRecentFoodEvent();

  @override
  List<Object> get props => [];
}

class LoadFavoriteFoodEvent extends FoodDatabaseEvent {
  const LoadFavoriteFoodEvent();

  @override
  List<Object> get props => [];
}

class LoadMyMealsEvent extends FoodDatabaseEvent {
  const LoadMyMealsEvent();

  @override
  List<Object> get props => [];
}

class LoadDatabaseFoodEvent extends FoodDatabaseEvent {
  const LoadDatabaseFoodEvent();

  @override
  List<Object> get props => [];
}

class SearchFoodEvent extends FoodDatabaseEvent {
  final SearchMealsParams params;

  const SearchFoodEvent({required this.params});
}