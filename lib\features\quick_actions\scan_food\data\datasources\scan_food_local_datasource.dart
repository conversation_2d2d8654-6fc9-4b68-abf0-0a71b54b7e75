// import 'package:cal/core/network/exceptions.dart';
// import 'package:camera/camera.dart';
// import 'package:dartz/dartz.dart';
// import 'package:injectable/injectable.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:permission_handler/permission_handler.dart';

// @lazySingleton
// class ScanFoodLocalDataSource {
//   final ImagePicker _imagePicker = ImagePicker();

//   Future<Either<Failure, bool>> checkCameraPermission({bool forceRequest = false}) async {
//     try {
//       PermissionStatus status = await Permission.camera.status;

//       if (forceRequest || status == PermissionStatus.denied) {
//         status = await Permission.camera.request();
//       }

//       return Right(status.isGranted);
//     } catch (e) {
//       return Left(Failure(message: 'Failed to check camera permission: $e'));
//     }
//   }

//   Future<Either<Failure, CameraController>> initializeCamera() async {
//     try {
//       final cameras = await availableCameras();

//       if (cameras.isEmpty) {
//         return const Left(Failure(message: 'No cameras available'));
//       }

//       final controller = CameraController(
//         cameras.first,
//         ResolutionPreset.high,
//         enableAudio: false,
//         imageFormatGroup: ImageFormatGroup.jpeg,
//       );

//       await controller.initialize();
//       return Right(controller);
//     } catch (e) {
//       return Left(Failure(message: 'Failed to initialize camera: $e'));
//     }
//   }

//   Future<Either<Failure, String>> captureImage(CameraController controller) async {
//     try {
//       if (!controller.value.isInitialized) {
//         return const Left(Failure(message: ('Camera not initialized')));
//       }

//       final XFile image = await controller.takePicture();
//       return Right(image.path);
//     } catch (e) {
//       return Left(Failure(message: "Failed to capture image: $e'"));
//     }
//   }

//   Future<Either<Failure, String?>> pickImageFromGallery() async {
//     try {
//       final XFile? image = await _imagePicker.pickImage(
//         source: ImageSource.gallery,
//         imageQuality: 80,
//       );

//       if (image == null) {
//         return const Right(null);
//       }

//       return Right(image.path);
//     } catch (e) {
//       return Left(Failure(message: 'Failed to check camera permission: $e'));
//     }
//   }
// }
