// import 'package:cal/features/quick_actions/scan_food/domain/entities/food_recognition_entity.dart';

// class FoodRecognitionModel extends FoodModel {
//   const FoodRecognitionModel({
//     required String foodName,
//     required double calories,
//     required double protein,
//     required double carbs,
//     required double fat,
//     required bool isHalal,
//     required List<String> ingredients,
//     required String imageUrl,
//   }) : super(
//     foodName: foodName,
//     calories: calories,
//     protein: protein,
//     carbs: carbs,
//     fat: fat,
//     isHalal: isHalal,
//     ingredients: ingredients,
//     imageUrl: imageUrl,
//   );

//   factory FoodRecognitionModel.fromJson(Map<String, dynamic> json) {
//     return FoodRecognitionModel(
//       foodName: json['food_name'] ?? '',
//       calories: (json['calories'] as num?)?.toDouble() ?? 0.0,
//       protein: (json['protein'] as num?)?.toDouble() ?? 0.0,
//       carbs: (json['carbs'] as num?)?.toDouble() ?? 0.0,
//       fat: (json['fat'] as num?)?.toDouble() ?? 0.0,
//       isHalal: json['is_halal'] ?? true,
//       ingredients: List<String>.from(json['ingredients'] ?? []),
//       imageUrl: json['image_url'] ?? '',
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'food_name': foodName,
//       'calories': calories,
//       'protein': protein,
//       'carbs': carbs,
//       'fat': fat,
//       'is_halal': isHalal,
//       'ingredients': ingredients,
//       'image_url': imageUrl,
//     };
//   }
// }