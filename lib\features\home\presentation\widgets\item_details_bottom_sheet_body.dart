import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/features/home/<USER>/widgets/cals_container.dart';
import 'package:cal/features/home/<USER>/widgets/ingredient_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../common/widgets/app_image.dart';
import '../../../../generated/assets.dart';
import '../bloc/recent_food_bloc.dart';
import '../pages/edit_value_screen.dart';

class ItemDetailsBottomSheetBody extends StatelessWidget {
  ItemDetailsBottomSheetBody({
    super.key,
    required this.controller,
    required this.foodModel,
    this.targetCalories = 2000,
    this.targetProtein = 150,
    this.targetCarbs = 250,
    this.targetFat = 65,
  });

  final ScrollController controller;
  final FoodModel foodModel;
  final double targetCalories;
  final double targetProtein;
  final double targetCarbs;
  final double targetFat;

  final List<String> images = [
    Assets.imagesProtien,
    Assets.imagesCarbs,
    Assets.imagesFats,
  ];

  void _navigateToEditValue(BuildContext context, NutritionType type) {
    double targetValue;
    double consumedValue;

    switch (type) {
      case NutritionType.calories:
        targetValue = targetCalories;
        consumedValue = foodModel.calories?.toDouble() ?? 0.0;
        break;
      case NutritionType.protein:
        targetValue = targetProtein;
        consumedValue = foodModel.protein ?? 0.0;
        break;
      case NutritionType.carbs:
        targetValue = targetCarbs;
        consumedValue = foodModel.carbs ?? 0.0;
        break;
      case NutritionType.fat:
        targetValue = targetFat;
        consumedValue = foodModel.fat ?? 0.0;
        break;
    }
    final recentFoodBloc = context.read<RecentFoodBloc>();

    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (ctx, animation, secondaryAnimation) => BlocProvider.value(
          value: recentFoodBloc,
          child: EditValueScreen(
            foodModel: foodModel,
            nutritionType: type,
            targetValue: targetValue,
            consumedValue: consumedValue,
            // you can actually drop the recentFoodBloc param from the ctor
            // and just rely on context.read() inside EditValueScreen if you want
          ),
        ),
        transitionsBuilder: (ctx, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

          final offsetAnimation = animation.drive(tween);
          return SlideTransition(
            position: offsetAnimation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 30),
      controller: controller,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (foodModel.date != null)
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: context.onPrimaryColor,
                ),
                padding: const EdgeInsetsDirectional.all(5),
                child: Text(
                  DateFormat('h:mm a').format(foodModel.date!),
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: foodModel.isHalal != false ? const Color(0xff27AE60) : Colors.red,
                ),
                padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 5),
                child: Text(
                  foodModel.isHalal != false ? LocaleKeys.common_halal.tr() : LocaleKeys.common_haram.tr(),
                  style: context.textTheme.labelLarge!.copyWith(
                    color: context.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                (context.locale == const Locale('ar') ? foodModel.arabicName : foodModel.englishName) ?? "Unknown",
                style: context.textTheme.titleMedium!.copyWith(
                  color: context.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Padding(
              padding: const EdgeInsetsDirectional.only(end: 20),
              child: Icon(
                Icons.bookmark_border_outlined,
                color: context.primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 18),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.common_servings.tr(),
              style: context.textTheme.titleSmall!.copyWith(
                color: context.onSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20), color: context.onPrimaryColor, border: Border.all(color: context.onSecondary)),
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 30, vertical: 10),
              child: Row(
                children: [
                  Text(
                    '1',
                    style: context.textTheme.bodyMedium!.copyWith(
                      color: context.onSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Icon(
                    Icons.mode_edit_outline,
                    color: context.onSecondary,
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),

        AppGestureDetector(
            onTap: () {
              _navigateToEditValue(context, NutritionType.calories);
            },
            child: CalsContainer(value: foodModel.calories)),

        const SizedBox(height: 10),

        // Clickable Macronutrients Row
        Row(
          spacing: 6,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: AppGestureDetector(
                onTap: () {
                  _navigateToEditValue(context, NutritionType.protein);
                },
                child: IngredientCard(image: Assets.imagesProtien, item: LocaleKeys.common_calories.tr(), value: foodModel.protein),
              ),
            ),
            Expanded(
              child: AppGestureDetector(
                onTap: () {
                  _navigateToEditValue(context, NutritionType.fat);
                },
                child: IngredientCard(image: Assets.imagesFats, item: LocaleKeys.common_fat.tr(), value: foodModel.fat),
              ),
            ),
            Expanded(
              child: AppGestureDetector(
                onTap: () {
                  _navigateToEditValue(context, NutritionType.carbs);
                },
                child: IngredientCard(image: Assets.imagesCarbs, item: LocaleKeys.common_carbs.tr(), value: foodModel.carbs),
              ),
            ),
          ],
        ),

        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: context.onSecondary.withAlpha(29),
              width: 1,
            ),
            color: context.onPrimaryColor,
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsetsDirectional.symmetric(horizontal: 10, vertical: 15),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: context.background,
                ),
                padding: const EdgeInsetsDirectional.all(10),
                child: const AppImage.asset(
                  Assets.imagesBrokenHeart,
                  width: 25,
                  height: 25,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          LocaleKeys.common_health_score.tr(),
                          style: context.textTheme.labelLarge!.copyWith(
                            color: context.onSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '7 / 10',
                          style: context.textTheme.bodySmall!.copyWith(
                            color: context.onSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    LinearProgressIndicator(
                      color: context.primaryColor,
                      value: .7,
                      backgroundColor: context.onSecondary.withAlpha(25),
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        // const SizedBox(height: 14),
        // Text(
        //   'المكونات',
        //   style: context.textTheme.titleSmall!.copyWith(
        //     color: context.onSecondary,
        //     fontWeight: FontWeight.bold,
        //   ),
        // ),
        // const SizedBox(height: 8),
        // Row(
        //   children: [
        //     Container(
        //       decoration: BoxDecoration(
        //         border: Border.all(
        //           color: context.onSecondary.withAlpha(29),
        //           width: 1,
        //         ),
        //         color: context.onPrimaryColor,
        //         borderRadius: BorderRadius.circular(12),
        //       ),
        //       height: 80,
        //       width: 100,
        //       padding: const EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 10),
        //       child: Column(
        //         children: [
        //           Text(
        //             'اضافة',
        //             style: context.textTheme.labelLarge!.copyWith(
        //               color: context.onSecondary,
        //               fontWeight: FontWeight.w400,
        //             ),
        //           ),
        //           const SizedBox(height: 5),
        //           Icon(
        //             Icons.add,
        //             size: 20,
        //             color: context.onSecondary,
        //           ),
        //         ],
        //       ),
        //     ),
        //   ],
        // ),
      ],
    );
  }
}
