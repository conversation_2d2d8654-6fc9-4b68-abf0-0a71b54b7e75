import 'package:cal/core/di/injection.dart';
import 'package:cal/core/network/http_client.dart';
import 'package:cal/features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart';
import 'package:cal/features/quick_actions/exercise/data/repositories/exercise_repository_impl.dart';
import 'package:cal/features/quick_actions/exercise/domain/repositories/exercise_repository.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart';
import 'package:cal/features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';


void initExercise() {
  // Data sources
  getIt.registerLazySingleton<ExerciseRemoteDataSource>(
    () => ExerciseRemoteDataSourceImpl(httpClient: getIt<HTTPClient>()),
  );

  // Repositories
  getIt.registerLazySingleton<ExerciseRepository>(
    () => ExerciseRepositoryImpl(remoteDataSource: getIt()),
  );

  getIt.registerLazySingleton<SaveExerciseUseCase>(
    () => SaveExerciseUseCase(getIt()),
  );
  getIt.registerLazySingleton<SaveExerciseAiUseCase>(
    () => SaveExerciseAiUseCase(getIt()),
  );

  // Blocs
  getIt.registerFactory<ExerciseBloc>(
    () => ExerciseBloc(
      saveExerciseUseCase: getIt(),
      saveExerciseAiUseCase: getIt(),
    ),
  );

  // External dependencies
  // Note: Dio or HTTPClient should ideally be registered in a core module
  // If HTTPClient is not already registered, uncomment the line below to register Dio
  // getIt.registerLazySingleton<Dio>(() => Dio());
}
