import 'package:cal/features/quick_actions/exercise/data/models/exercise_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_ai_model.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:dartz/dartz.dart';

abstract class ExerciseRepository {
  Future<Either<String, ExerciseModel>> saveExercise(ExerciseSaveModel exercise);
  Future<Either<String, ExerciseModel>> saveExerciseAi(ExerciseSaveAiModel exercise);
}
