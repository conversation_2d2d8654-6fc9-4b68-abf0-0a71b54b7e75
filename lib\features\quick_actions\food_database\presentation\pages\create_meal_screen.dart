import 'dart:developer';

import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/theme/text_theme.dart';
import 'package:cal/common/widgets/app_gesture_detector.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/common/widgets/large_button.dart';
import 'package:cal/common/widgets/manual_nutrition_edit_screen.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/onboarding/presentation/widgets/onboarding_metric_card.dart';
import 'package:cal/features/quick_actions/food_database/data/models/database_food_model.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/pages/database_list_screen.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/enter_your_food_container.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/nutristion_container.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CreateMealScreen extends StatefulWidget {
  const CreateMealScreen({super.key});

  @override
  State<CreateMealScreen> createState() => _CreateMealScreenState();
}

class _CreateMealScreenState extends State<CreateMealScreen> {
  List<DatabaseFoodModel> selectedFoods = [];
  String? mealName;
  String mealCals = "0";
  String mealProtien = "0";
  String mealCarb = "0";
  String mealFat = "0";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      appBar: AppBar(
        forceMaterialTransparency: true,
        elevation: 0,
        centerTitle: true,
        leading: Padding(
          padding: const EdgeInsets.only(right: 18.0),
          child: GestureDetector(
              onTap: () {
                context.pop();
              },
              child: const Icon(Icons.arrow_back)),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        title: Text(
          LocaleKeys.food_database_create_meal.tr(),
          style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w300, color: context.primaryColor),
          textAlign: TextAlign.start,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 26, start: 26, top: 2, bottom: 50),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                spacing: 8,
                children: [
                  AppGestureDetector(
                    onTap: () async {
                      final result = await context.push(ManualNutritionEditScreen(
                        nutritionValue: mealCals,
                        cardType: CardType.cals,
                      ));
                      if (result != null) {
                        setState(() {
                          mealCals = result;
                        });
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 20),
                      decoration: BoxDecoration(
                        color: context.onPrimaryColor,
                        border: Border.all(color: context.onSecondary.withAlpha(50), width: 1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(3),
                        child: Row(
                          spacing: 12,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: context.onPrimaryContainer.withAlpha(210),
                              ),
                              child: const AppImage.asset(Assets.imagesCals),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  LocaleKeys.common_calories.tr(),
                                  textAlign: TextAlign.center,
                                  style: textTheme.bodyMedium!.copyWith(color: Theme.of(context).colorScheme.onSecondary, fontWeight: FontWeight.bold, fontSize: 10),
                                ),
                                Text(
                                  mealCals,
                                  textAlign: TextAlign.start,
                                  style: textTheme.bodyMedium!.copyWith(color: context.colorScheme.primary, fontWeight: FontWeight.bold, fontSize: 24),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Row(
                    spacing: 6,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      NutristionContainer(
                        value: mealProtien,
                        cardType: CardType.protien,
                        onTap: () async {
                          final result = await context.push(ManualNutritionEditScreen(
                            nutritionValue: mealProtien,
                            cardType: CardType.protien,
                          ));
                          if (result != null) {
                            setState(() {
                              mealProtien = result;
                            });
                          }
                        },
                      ),
                      NutristionContainer(
                        value: mealCarb,
                        cardType: CardType.carbs,
                        onTap: () async {
                          final result = await context.push(ManualNutritionEditScreen(
                            nutritionValue: mealCarb,
                            cardType: CardType.carbs,
                          ));
                          if (result != null) {
                            setState(() {
                              mealCarb = result;
                            });
                          }
                        },
                      ),
                      NutristionContainer(
                        value: mealFat,
                        cardType: CardType.fat,
                        onTap: () async {
                          final result = await context.push(ManualNutritionEditScreen(
                            nutritionValue: mealFat,
                            cardType: CardType.fat,
                          ));
                          if (result != null) {
                            setState(() {
                              mealFat = result;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 50),
                  const AppImage.asset(
                    Assets.onboardingPlate,
                    size: 35,
                  ),
                  AppText.displaySmall(
                    LocaleKeys.food_database_meal_nutarians.tr(),
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(height: 10),
                  EnterYourFoodContainer(
                    onPressed: () async {
                      final selectedFood = await context.push(
                        BlocProvider(
                          create: (context) => getIt<FoodDatabaseBloc>(),
                          child: const DatabaseListScreen(),
                        ),
                      );

                      log("on press worked");
                      if (selectedFood != null) {
                        setState(() {
                          log("got added");
                          selectedFoods.add(selectedFood);
                        });
                      }
                    },
                    verticalaPadding: 15,
                    text: LocaleKeys.food_database_add_nutrians_to_this_meal.tr(),
                    icon: const Icon(Icons.add),
                  ),
                  BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
                    builder: (context, state) {
                      return Column(
                        children: state.selectedFoods.map((food) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 10.0),
                            child: FoodDatabaseCard(
                              title: food.dish ?? "Unknown",
                              cals: food.calories.toString(),
                              onAddTap: () {},
                            ),
                          );
                        }).toList(),
                      );
                    },
                  ),
                ],
              ),
              LargeButton(
                onPressed: () {
                  context.read<FoodDatabaseBloc>().add(
                        AddFoodEvent(
                          meal: DatabaseFoodModel(
                            calories: int.parse(mealCals),
                            carbs: double.parse(mealCarb),
                            fat: double.parse(mealFat),
                            date: DateTime.now(),
                            dish: mealName,
                          ),
                        ),
                      );
                },
                text: LocaleKeys.food_database_food_database.tr(),
                backgroundColor: context.primaryColor,
                textStyle: TextStyle(color: context.onPrimaryColor),
                circularRadius: 12,
              )
            ],
          ),
        ),
      ),
    );
  }
}
