import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/core/di/injection.dart';
import 'package:cal/features/quick_actions/exercise/data/models/exercise_save_model.dart';
import 'package:cal/features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WeightLiftingExerciseScreen extends StatefulWidget {
  const WeightLiftingExerciseScreen({super.key});

  @override
  State<WeightLiftingExerciseScreen> createState() => _WeightLiftingExerciseScreenState();
}

class _WeightLiftingExerciseScreenState extends State<WeightLiftingExerciseScreen> {
  double _currentSliderValue = 15;
  String _selectedIntensity = 'high';

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ExerciseBloc>(
      create: (context) => getIt<ExerciseBloc>(),
      child: BlocListener<ExerciseBloc, ExerciseState>(
        listener: (context, state) {
          if (state is ExerciseSaved) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Exercise saved successfully!')),
            );
            Navigator.of(context).pop();
          } else if (state is ExerciseError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error: ${state.message}')),
            );
          }
        },
        child: Scaffold(
          appBar: AppBar(
            title: AppText.titleLarge(
              'Log Exercise(Weight Lifting)',
              color: context.onSecondary,
            ),
            centerTitle: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText.titleLarge(
                  'ضبط الشدة',
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 20),
                _buildIntensityOption(
                  context,
                  'عالي',
                  'التدريب حتى الفشل العضلي، التنفس بصعوبة',
                  'high',
                ),
                const SizedBox(height: 16),
                _buildIntensityOption(
                  context,
                  'متوسط',
                  'التعرق، العديد من التكرارات',
                  'mid',
                ),
                const SizedBox(height: 16),
                _buildIntensityOption(
                  context,
                  'منخفض',
                  'لا العرق بذل القليل من الجهد',
                  'low',
                ),
                const SizedBox(height: 30),
                AppText.titleLarge(
                  'المدة',
                  color: context.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildDurationButton(context, 15),
                    _buildDurationButton(context, 30),
                    _buildDurationButton(context, 60),
                    _buildDurationButton(context, 90),
                  ],
                ),
                Slider(
                  value: _currentSliderValue,
                  max: 120,
                  divisions: 24,
                  label: _currentSliderValue.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      _currentSliderValue = value;
                    });
                  },
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final exercise = ExerciseSaveModel(
                        type: 'Weight Lifting',
                        calories: 0, // Calories will be calculated on the backend
                        intensity: _selectedIntensity,
                        duration: _currentSliderValue.round(),
                      );
                      context.read<ExerciseBloc>().add(SaveExercise(exercise: exercise));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: AppText.titleLarge(
                      'التالي',
                      color: context.onPrimaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIntensityOption(
    BuildContext context,
    String title,
    String subtitle,
    String intensityValue,
  ) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIntensity = intensityValue;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(
            color: _selectedIntensity == intensityValue ? context.primaryColor : Colors.transparent,
            width: 2.0,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppText.titleMedium(
              title,
              color: context.onSecondary,
              fontWeight: FontWeight.bold,
            ),
            AppText.bodySmall(
              subtitle,
              color: context.onSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDurationButton(BuildContext context, int duration) {
    return OutlinedButton(
      onPressed: () {
        setState(() {
          _currentSliderValue = duration.toDouble();
        });
      },
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: context.primaryColor),
        backgroundColor: _currentSliderValue == duration ? context.primaryColor : Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: AppText.bodyMedium(
        '$duration دقيقة',
        color: _currentSliderValue == duration ? context.onPrimaryColor : context.primaryColor,
      ),
    );
  }
}


