import 'dart:async';
import 'dart:math';
import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LoadingTextAnimation extends StatefulWidget {
  const LoadingTextAnimation({super.key});

  @override
  State<LoadingTextAnimation> createState() => _LoadingTextAnimationState();
}

class _LoadingTextAnimationState extends State<LoadingTextAnimation> {
  int _currentIndex = 0;
  Timer? _timer;

  final List<String> _loadingTexts = [
    LocaleKeys.home_analyzing,
    LocaleKeys.home_calculating_nutrition,
    LocaleKeys.home_processing_image,
    LocaleKeys.home_identifying_food,
    LocaleKeys.home_computing_calories,
  ];

  @override
  void initState() {
    super.initState();
    _startAnimation();
  }

  void _startAnimation() {
    final random = Random();
    int previousIndex = _currentIndex;

    _timer = Timer.periodic(const Duration(milliseconds: 2500), (timer) {
      if (!mounted) return;

      int newIndex;
      do {
        newIndex = random.nextInt(_loadingTexts.length);
      } while (newIndex == previousIndex);

      setState(() {
        _currentIndex = newIndex;
        previousIndex = newIndex;
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 350),
      child: Text(
        _loadingTexts[_currentIndex].tr(),
        key: ValueKey(_currentIndex),
        textAlign: TextAlign.start,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: context.onSecondaryContainer,
        ),
      ),
    );
  }
}
