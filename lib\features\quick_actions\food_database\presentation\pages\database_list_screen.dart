import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/common/widgets/custom_text_field.dart';
import 'package:cal/features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart';
import 'package:cal/features/quick_actions/food_database/presentation/widgets/food_database_card.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DatabaseListScreen extends StatefulWidget {
  const DatabaseListScreen({super.key});

  @override
  State<DatabaseListScreen> createState() => _CreateMealScreenState();
}

class _CreateMealScreenState extends State<DatabaseListScreen> {
  final TextEditingController mealController = TextEditingController();
  String _searchQuery = '';
  String? mealName;
  String mealCals = "0";
  String mealProtien = "0";
  String mealCarb = "0";
  String mealFat = "0";

  @override
  void initState() {
    super.initState();
    context.read<FoodDatabaseBloc>().add(const LoadDatabaseFoodEvent());
    mealController.addListener(() {
      setState(() {
        _searchQuery = mealController.text.trim().toLowerCase();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      appBar: AppBar(
        forceMaterialTransparency: true,
        elevation: 0,
        centerTitle: true,
        leading: Padding(
          padding: const EdgeInsets.only(right: 18.0),
          child: GestureDetector(
              onTap: () {
                context.pop();
              },
              child: const Icon(Icons.arrow_back)),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        title: Text(
          LocaleKeys.food_database_create_meal.tr(),
          style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w300, color: context.primaryColor),
          textAlign: TextAlign.start,
        ),
      ),
      body: BlocBuilder<FoodDatabaseBloc, FoodDatabaseState>(
        builder: (context, state) {
          final filteredDatabaseList =
              state.databaseFoodList.where((item) => item.dish?.toLowerCase().contains(_searchQuery) ?? false).toList();

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsetsDirectional.only(end: 26, start: 26, top: 2, bottom: 50),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomTextField(
                      controller: mealController, hint: LocaleKeys.food_database_describe_what_you_ate.tr()),
                  const SizedBox(height: 20),
                  Align(
                    alignment: Alignment.centerRight,
                    child: AppText.titleLarge(
                      LocaleKeys.food_database_food_database.tr(),
                      color: context.onSecondary,
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                  const SizedBox(height: 19),
                  state.databaseFoodList.isNotEmpty
                      ? ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, i) => FoodDatabaseCard(
                              title: filteredDatabaseList[i].dish ?? "unknown",
                              cals: filteredDatabaseList[i].calories.toString(),
                              onAddTap: () {
                                context.read<FoodDatabaseBloc>().add(AddSelectedFoodEvent(filteredDatabaseList[i]));
                                context.pop();
                              }),
                          separatorBuilder: (context, index) => const SizedBox(height: 16),
                          itemCount: filteredDatabaseList.length,
                        )
                      : Align(
                          alignment: Alignment.center,
                          child: AppText.bodyMedium(
                            LocaleKeys.food_database_no_food.tr(),
                            color: context.onSecondary,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
