import 'package:cal/core/local_models/daily_data_model/daily_data_model.dart';
import 'package:cal/features/home/<USER>/datasources/home_remote_datasource.dart';
import 'package:cal/features/home/<USER>/repositories/home_repository.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: HomeRepository)
class HomeRepositoryImpl implements HomeRepository {
  final HomeLocalDataSource localDataSource;

  HomeRepositoryImpl({required this.localDataSource});

  @override
  Future<DailyUserDataModel> getDailyUserData(DateTime date) {
    return localDataSource.getDailyUserData(date);
  }

  @override
  Future<void> updateDailyUserData(DailyUserDataModel dailyUserData) {
    return localDataSource.updateDailyUserData(dailyUserData);
  }
}
