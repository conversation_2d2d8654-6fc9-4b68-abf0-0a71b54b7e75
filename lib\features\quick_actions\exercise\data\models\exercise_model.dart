import 'package:equatable/equatable.dart';

class ExerciseModel extends Equatable {
  final String id;
  final String name;
  final String type;
  final int duration;
  final int caloriesBurned;

  const ExerciseModel({
    required this.id,
    required this.name,
    required this.type,
    required this.duration,
    required this.caloriesBurned,
  });

  factory ExerciseModel.fromJson(Map<String, dynamic> json) {
    return ExerciseModel(
      id: json["id"],
      name: json["name"],
      type: json["type"],
      duration: json["duration"],
      caloriesBurned: json["caloriesBurned"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "name": name,
      "type": type,
      "duration": duration,
      "caloriesBurned": caloriesBurned,
    };
  }

  @override
  List<Object?> get props => [id, name, type, duration, caloriesBurned];
}


